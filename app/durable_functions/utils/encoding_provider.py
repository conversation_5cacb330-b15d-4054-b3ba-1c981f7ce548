import tiktoken


encoding_cache: dict[str, tiktoken.Encoding] = {}


def get_encoding(model_name: str):
    encoding = encoding_cache.get(model_name, None)
    if encoding:
        return encoding

    encoding = tiktoken.encoding_for_model(model_name)
    encoding_cache[model_name] = encoding
    return encoding


def calculate_tokens(model_name: str, text: str) -> int:
    encoding = get_encoding(model_name)
    return len(encoding.encode(text))
